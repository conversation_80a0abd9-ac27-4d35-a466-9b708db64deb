<!DOCTYPE HTML>
<!--
	<PERSON> Henley - Thank You
	Based on Dopetrope by HTML5 UP
-->
<html>
<head>
    <title>Thank You | Studio Henley - Professional Painting & Decorating</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
    <link rel="stylesheet" href="assets/css/main.css" />
    <style>
        body {
            background-color: #3A4435;
            color: #F5F5F5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .thank-you-content {
            background: #F5F5F5;
            border-radius: 10px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.1);
            padding: 3rem 2rem;
            text-align: center;
            max-width: 500px;
            width: 100%;
            border: 2px solid #8B9A7A;
        }
        .thank-you-content h1 {
            font-family: 'Source Sans Pro', sans-serif;
            font-size: 2.2rem;
            margin-bottom: 1rem;
            color: #3A4435;
            font-weight: 700;
        }
        .thank-you-content p {
            font-size: 1.1rem;
            color: #3A4435;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .icon-success {
            width: 80px;
            height: 80px;
            margin-bottom: 1.5rem;
            display: inline-block;
            animation: popIn 0.7s cubic-bezier(.68,-0.55,.27,1.55) 0.2s both;
        }
        @keyframes popIn {
            0% { transform: scale(0.2); opacity: 0; }
            60% { transform: scale(1.2); opacity: 1; }
            80% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .icon-success path {
            stroke-dasharray: 100;
            stroke-dashoffset: 100;
            transition: stroke-dashoffset 1.4s cubic-bezier(.68,-0.55,.27,1.55);
        }
        .button {
            background-color: #CD8B5C;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.8rem 2rem;
            font-size: 1rem;
            font-family: 'Source Sans Pro', sans-serif;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #b37a4a;
        }
        .phone-link {
            color: #CD8B5C;
            font-weight: 600;
            text-decoration: none;
        }
        .phone-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body class="is-preload">
    <div id="page-wrapper">
        <!-- Header -->
        <header id="header">
            <h1><a href="index.html">Studio Henley</a></h1>
        </header>

        <!-- Main Content -->
        <div class="thank-you-content">
            <svg class="icon-success" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="30" stroke="#8B9A7A" stroke-width="4" fill="none"/>
                <path id="tick" d="M20 32L28 40L44 24" stroke="#CD8B5C" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="100" stroke-dashoffset="100"/>
            </svg>
            <h1>Thank You!</h1>
            <p>We've received your message and will be in touch with you shortly.</p>
            <p>For immediate assistance, please call us at: <br><a href="tel:07913149507" class="phone-link">07913 149507</a></p>
            <a href="index.html" class="button">Back to Home</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/jquery.min.js"></script>
    <script>
    // Animate the checkmark
    document.addEventListener('DOMContentLoaded', function() {
        const tick = document.getElementById('tick');
        setTimeout(() => {
            tick.style.strokeDashoffset = '0';
        }, 400);
    });
    </script>
</body>
</html>
