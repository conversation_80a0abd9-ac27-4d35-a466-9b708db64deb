# <PERSON> Henley — Integration Handbook for the Existing Site

This handbook shows—step by step—how to evolve your **current static HTML site** into the fully-optimised, blog-enabled, SEO-ready platform mapped in the earlier blueprint. All instructions assume the exact file structure you listed (index.html, gallery.html, about.html, contact.html, etc.) and the asset directories already in place. Follow each section sequentially to migrate with zero downtime and preserve every existing URL.

## Overview

Studio Henley presently runs on a lean flat-file architecture. You will:

- Create *new* directories (`/blog/`, `/services/`, `/areas/`) without touching core pages.
- Add shared partials (`header.html`, `footer.html`, `sidebar.html`) so future edits propagate everywhere.
- Upgrade technical SEO (titles, meta, structured data, Core Web Vitals).
- Implement the keyword strategy and blog workflow delivered earlier.

> **Goal:** keep the project small-footprint (no database), yet robust enough to handle 50–100 evergreen posts plus location pages.

## Current Tree vs. Target Tree

| Status | Current Path | Action | Target Path | Notes |
|---|---|---|---|---|
| ✅ | `/index.html` | Keep | `/index.html` | Inject “Latest Blog Posts” carousel. |
| ✅ | `/gallery.html` | Keep | `/gallery.html` | Swap `<img>` for lazy-loaded `<img loading="lazy">`. |
| ✅ | `/about.html` | Keep | `/about.html` | Add FAQ accordion + FAQPage schema. |
| ✅ | `/contact.html` | Keep | `/contact.html` | Embed Google Map iframe; add “get a painting quote” CTA. |
| 🆕 | *(none)* | **Create** | `/blog/index.html` | Static list view (10 posts per page). |
| 🆕 | *(none)* | **Create** | `/blog/YYYY/MM/DD/slug.html` | Individual post template. |
| 🆕 | *(none)* | **Create** | `/services/interior-painting.html` (etc.) | One page per service keyword. |
| 🆕 | *(none)* | **Create** | `/areas/henley-on-thames.html` (etc.) | One landing page per town/postcode. |
| 🆕 | *(none)* | **Partial** | `/partials/header.html` | Holds nav + logo. |
| 🆕 | *(none)* | **Partial** | `/partials/footer.html` | Holds contact info, local links. |
| 🆕 | *(none)* | **Partial** | `/partials/sidebar.html` | Re-use in blog layouts. |

All new files inherit your `main.css` and share existing fonts/icons—no design refactor required.

## 1. Global Partials

### 1.1 Header (`/partials/header.html`)

```html
<header id="site-header">
  <div class="logo">
    <a href="/"><img src="/images/logo.png" alt="Studio Henley painters and decorators logo"></a>
  </div>

  <nav aria-label="Main navigation">
    <ul>
      <li><a href="/">Home</a></li>
      <li><a href="/services/interior-painting.html">Services</a>
        <ul>
          <li><a href="/services/interior-painting.html">Interior Painting</a></li>
          <li><a href="/services/exterior-painting.html">Exterior Painting</a></li>
          <li><a href="/services/wallpapering.html">Wallpapering</a></li>
        </ul>
      </li>
      <li><a href="/gallery.html">Gallery</a></li>
      <li><a href="/blog/">Blog</a></li>
      <li><a href="/about.html">About</a></li>
      <li><a href="/contact.html">Contact</a></li>
    </ul>
  </nav>
</header>
```

*Benefits:* one-line include in every page; future nav changes ripple site-wide.

### 1.2 Footer (`/partials/footer.html`)

Embed NAP (Name-Address-Phone) once plus micro-links to each `/areas/` page for strong local signals.

```html
<footer>
  <p>Studio Henley, Henley-on-Thames RG9 • 0118 XYZ 1234 • Mon–Fri 08:00-18:00</p>
  <nav aria-label="Service Areas">
    Henley-on-Thames | Marlow | Shiplake | Wallingford | Sonning | Wargrave | Twyford
  </nav>
</footer>
```

## 2. Core Web Vitals Enhancements

| Issue | Fix | Reference |
|---|---|---|
| **Largest Contentful Paint** over 2.5 s on hero images | Compress to ≤250 KB + preload hero `<link rel="preload">` + inline Critical CSS. | Google LCP threshold 2.5 s |
| **Image overload in gallery** | Add `loading="lazy"` to every non-hero `<img>` and supply `width`+`height` attributes to cut CLS. | HTML lazy loading spec |
| **Render-blocking JS** | Defer `main.js` and third-party scripts with `defer` attribute; move jQuery CDN to foot. | Google performance docs |

After changes, 75% of visits should hit the “Good” LCP bucket (<2.5 s).

## 3. SEO Meta & Structured Data

### 3.1 Page-Level Meta

| Page | `<title>` Example | `<meta description>` Example |
|---|---|---|
| Home | Painting & Decorating in Henley-on-Thames | Studio Henley | Professional painters & decorators in Henley-on-Thames offering interior, exterior & wallpapering. Free painting estimates & trusted workmanship. |
| Interior Service | Interior Painting | Studio Henley | High-quality interior house painting for RG9 homeowners. Book a free quote with certified painters. |
| Blog Post | Earth-Tone House Painting in Henley: Brown Is Back | Discover how our painters in Henley-on-Thames use warm browns to transform period homes—see before & after shots. |

Target length: ≤60 chars for titles, 120-160 chars for descriptions.

### 3.2 LocalBusiness JSON-LD

Place **only** on `index.html` and each area page.

```html
<script type="application/ld+json">
{
 "@context":"https://schema.org",
 "@type":"LocalBusiness",
 "name":"Studio Henley",
 "image":"https://studiohenley.co.uk/images/logo.png",
 "url":"https://studiohenley.co.uk",
 "telephone":"+44 118 XYZ 1234",
 "address":{
   "@type":"PostalAddress",
   "streetAddress":"15 Market Place",
   "addressLocality":"Henley-on-Thames",
   "postalCode":"RG9 2XX",
   "addressCountry":"GB"
 },
 "openingHoursSpecification":[{
   "@type":"OpeningHoursSpecification",
   "dayOfWeek":["Monday","Tuesday","Wednesday","Thursday","Friday"],
   "opens":"08:00",
   "closes":"18:00"
 }],
 "priceRange":"££"
}
</script>
```

**Why:** eligible for knowledge-panel rich results.

### 3.3 FAQPage JSON-LD

Add to *any* page containing accordion questions—e.g., about.html.

```html
<script type="application/ld+json">
{
 "@context":"https://schema.org",
 "@type":"FAQPage",
 "mainEntity":[
   {
    "@type":"Question",
    "name":"How much does exterior house painting cost in Henley?",
    "acceptedAnswer":{
      "@type":"Answer",
      "text":"Our expert decorators provide free painting estimates. Exterior projects start at £2,000 for a standard 3-bed home."
    }
   }
 ]
}
</script>
```

Conforms to Google FAQ guidelines—boosts SERP real estate.

## 4. Keyword Deployment Plan

### 4.1 Core, Specific & Location Keywords

Embed keywords naturally; cap any exact phrase at 2% density to avoid stuffing penalties.

| Section | Keyword Placement | Example |
|---|---|---|
| Hero H1 | Core Service + Primary City | “Professional Painting & Decorating in Henley-on-Thames” |
| Sub-heading | Specific Service | “Interior Painting for Period Properties” |
| Body | Quality Descriptor | “…our **trusted painting** team delivers a brushless finish.” |
| Anchor | Long-Tail | `<a href="/contact.html">get a painting quote</a>` |
| Alt Text | Service + Location | `alt="Interior painting Henley living room—Studio Henley"` |

### 4.2 Image Alt & File Names

| Image | File Name | Alt Text |
|---|---|---|
| Hero brown wall | `house-painting-henley-brown.jpg` | “High-quality house painting in Henley-on-Thames—warm brown wall finish” |
| Gallery exterior | `exterior-painting-henley-rg9.jpg` | “Exterior painting RG9 cedar cladding—weatherproof spray finish” |
| Blog infographic | `eco-friendly-paints-rg9.png` | “Eco-friendly paints data for Henley-on-Thames homes” |

## 5. Blog Implementation

### 5.1 Directory & Permalinks

```
/blog/
  |-- index.html
  |-- 2025/
        |-- 08/
              |-- 15/earth-tone-house-painting-henley.html
              |-- 22/color-drenching-101.html
```

Permalink structure improves crawl depth and date context.

### 5.2 Blog Post Template (`/blog/post-template.html`)

```html
<!--#include file="/partials/header.html" -->
<article class="post">
  <h1>{{ POST_TITLE }}</h1>
  <p class="meta">Published {{ DATE }} • In <a href="/services/interior-painting.html">Interior Painting</a></p>

  <figure>
    <img src="/assets/img/2025/cocoa-wall.jpg" width="2400" height="1600" alt="House painting Henley—rich cocoa wall" loading="eager">
    <figcaption>Earth-tone makeover in RG9 period property.</figcaption>
  </figure>

  <!-- Post content here… -->
</article>

<aside class="sidebar">
  <!-- “Book a Free Colour Consult” form or CTA -->
</aside>
<!--#include file="/partials/footer.html" -->
```

### 5.3 Blog Index (`/blog/index.html`)

```html
<section class="post-feed">
  {{ LOOP THROUGH LATEST 10 POSTS }}
  <article>
    <a href="{{ post_url }}">
      <img src="{{ thumb }}" width="600" height="400" alt="{{ alt }}" loading="lazy">
      <h2>{{ post_title }}</h2>
    </a>
    <p>{{ excerpt }} <a href="{{ post_url }}">Read&nbsp;more »</a></p>
  </article>
  {{ END LOOP }}
</section>

<nav class="pagination">
  <a href="/blog/page/2/">Older Posts</a>
</nav>
```

Pagination keeps index light—helps LCP.

### 5.4 Rule of Three Internal Links

Within each post:

1. **Service anchor**: “See our interior painting services in Henley.”
2. **Area anchor**: “Trusted painters Marlow.”
3. **Blog anchor**: Link to earlier related post.

## 6. Service Pages Build-out

Create one static HTML per service—use left-sidebar.html as layout skeleton.

| File | Target Keyword | H1 | Core Sections |
|---|---|---|---|
| `/services/interior-painting.html` | interior painting Henley | “Interior Painting for Henley-on-Thames Homes” | Process, colour consultation, case study, CTA. |
| `/services/exterior-painting.html` | exterior painting Henley | “Weather-Proof Exterior Painting in RG9” | Prep, spray options, warranty, CTA. |
| `/services/wallpapering.html` | wallpapering Henley | “Professional Wallpaper Installer in Henley” | Types, patterns, cost guide, CTA. |

> Each page links to *one* area page and *two* blog posts for topical depth.

## 7. Area Pages Build-out

Use no-sidebar layout to avoid clutter.

| File | H1 | Required Blocks |
|---|---|---|
| `/areas/henley-on-thames.html` | “Painters & Decorators in Henley-on-Thames” | Intro, map iframe, case study, services list, contact CTA. |
| `/areas/marlow.html` | “Painters Marlow: Premium Painting Services” | Local landmarks reference, driving-time paragraph, testimonial slider, CTA. |

Each page embeds LocalBusiness schema *with modified address* where needed for multi-branch future growth.

## 8. JavaScript & CSS Optimisation

1. **Defer** all existing JS except breakpoints library.
2. **PurgeCSS** unused selectors in `main.css`; inline 10 KB Critical CSS in `<head>` for hero/above fold.
3. **Bundle** JS into `main.min.js` (≈25 KB Gzip) to reduce HTTP requests.

Outcome: **INP** drops below 150 ms.

## 9. Accessibility & Image Governance

- Always pair `<figure>` + `<figcaption>` for context.
- Provide `aria-label` on nav menus.
- Ensure colour contrast ratio ≥4.5:1 (WCAG AA).

## 10. Deployment & QA

### 10.1 Pre-launch Checklist

| Task | Pass/Fail |
|---|---|
| Lighthouse performance >90 mobile |  |
| W3C HTML validator zero errors |  |
| Screaming Frog: no 404s, no duplicate titles |  |
| Rich Results Test: LocalBusiness & FAQ valid |  |
| Core Web Vitals: LCP ≤2 s, CLS ≤0.05, INP ≤150 ms |  |

### 10.2 Roll-out Order

1. Push partials + updated CSS/JS.
2. Deploy service pages.
3. Update nav links.
4. Add area pages.
5. Deploy blog index (empty).
6. Publish first four cornerstone posts.
7. Upload XML sitemap via Search Console.

## 11. Post-Launch Content Governance

| Cadence | Task | Metric to Watch |
|---|---|---|
| Weekly | Publish one blog + social promo | Users, Avg. Time on Page |
| Monthly | Check Search Console “Queries” for new keywords | Click-through rate |
| Quarterly | Lighthouse + Core Web Vitals audit | LCP / CLS / INP trend |
| Annually | Refresh trend posts for 2026 design shifts | Organic sessions YoY |

## 12. Security & Backup

- Force HTTPS via `.htaccess` redirect.
- Enable weekly off-site backups (cron + rsync).
- Use Subresource Integrity (SRI) for CDNs.

## 13. Example Updated `index.html` Snippet

```html
<!--#include file="/partials/header.html" -->

<section id="hero">
  <h1>Professional Painting & Decorating in Henley-on-Thames</h1>
  <p>Local, insured painters delivering high-quality interior and exterior finishes.</p>
  <a class="button primary" href="/contact.html">Get a Free Painting Estimate</a>
</section>

<section id="latest-posts">
  <h2>Latest from the Blog</h2>
  <div class="post-grid">
    <!-- Repeat for 4 posts -->
    <article>
      <a href="/blog/2025/08/22/color-drenching-101.html">
        <img src="/assets/img/2025/teal-library-thumb.jpg" width="600" height="400" alt="Interior painting Henley color drenching example" loading="lazy">
        <h3>Color Drenching 101</h3>
      </a>
      <p>Learn how our Henley painters transform rooms with one bold hue.</p>
    </article>
  </div>
</section>

<!--#include file="/partials/footer.html" -->
```

## Conclusion

By grafting these enhancements onto your *existing* file tree, you unlock a scalable blog, richer local SEO, and faster load times—without migrating off static HTML. Implement each chapter of this handbook methodically:

1. **Partials** for DRY layouts.
2. **SEO meta + schema** to speak Google’s language.
3. **Core Web Vitals** tuning for sub-2 s LCP.
4. **Keyword matrix** woven naturally—no stuffing alarms.
5. **Structured content** (services, areas, blog) that funnels every visitor toward a *painting quote* CTA.

Finish the roll-out, submit your sitemap, and Studio Henley will stand out as the **trusted, high-quality painting company** for Henley-on-Thames and the wider Thames Valley—online and on the job.
